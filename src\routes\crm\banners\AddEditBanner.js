/* eslint-disable jsx-a11y/control-has-associated-label */
/**
 * Create Version  Page
 */
import React, { useState, useEffect } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { useHistory, useParams } from "react-router-dom";
import { useQuery, useMutation } from "@apollo/client";
import { Helmet } from "react-helmet";
import { FileUploader } from "components/ImageUploader";
import { ImageUpload } from "gql/mutations/UploadImage.gql";
import { CreateBanner } from "gql/mutations/CreateBanner.gql";
import { UpdateBanner } from "gql/mutations/UpdateBanner.gql";
import { NotificationManager } from "react-notifications";
import { Banner } from "gql/queries/getBanner.gql";
import CustomTextField from "components/Input/CustomTextField";
import Select from "react-select";
import { BannerStatus } from "constants/constants";
import { AllBanners } from "gql/queries/getAllBanners.gql";
import IntlMessages from "util/IntlMessages";
import PageTitleBar from "components/PageTitleBar/PageTitleBar";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import Checkbox from "@material-ui/core/Checkbox";
import { CitiesDropDown } from "components/DropDowns";
import AllyName from "components/DropDowns/AllyName";
import BranchesDropDown from "components/DropDowns/BranchesDropDown";
import CarVersionsDropDown from "components/DropDowns/CarVersionsDropDown";
import VehicleTypeDropDown from "components/DropDowns/VehicleTypeDropDown";
import ExtraServiceDropDown from "components/DropDowns/ExtraServicesDropDown";
import RentalPriceFilter from "./Prices";
import { Tooltip } from "@material-ui/core";
import swal from "sweetalert";

export default function AddEditBanner() {
  const { bannerId } = useParams();
  const [errors, setErrors] = useState();
  const { formatMessage } = useIntl();
  const history = useHistory();
  const [ArImage, setArImage] = useState();
  const [isActive, setIsActive] = useState();
  const [displayOrder, setDisplayOrder] = useState();
  const [loader, setLoader] = useState(false);
  const [Arloader, setArLoader] = useState(false);
  const [changed, setChanged] = useState(false);
  const [selectedCities, setSelectedCity] = useState([]);
  const [versions, SetSelectedVersions] = useState([]);
  const [branches, setSelectedBranches] = useState([]);

  const [imageUpload] = useMutation(ImageUpload);
  const {
    data: banner,
    refetch,
    loading: BannerLoader,
  } = useQuery(Banner, {
    skip: !bannerId,
    variables: {
      id: +bannerId,
    },
  });
  const { data: banners, refetch: allBanners } = useQuery(AllBanners, {
    variables: { page: 1, limit: 10 },
  });
  const [allyIds, setAllyIds] = useState([]);
  const [minPrice, setMinPrice] = useState("");
  const [maxPrice, setMaxPrice] = useState("");
  const [deeplink, setDeepLink] = useState(false);
  const [createBanner, { loading: CreateLoader }] = useMutation(CreateBanner);
  const [updateBanner, { loading: updateLoader }] = useMutation(UpdateBanner);
  const [EnImage, setEnImage] = useState();
  const [ExtraService, setSelectedExtraService] = useState([]);
  const [vehicleTypes, setSelectedVehicle] = useState([]);
  const handelSaveBanner = () => {
    if (bannerId) {
      updateBanner({
        variables: {
          displayOrder,
          id: +bannerId,
          imgAr: ArImage,
          imgEn: EnImage,
          isActive: isActive.value != "false",
          dailyPriceFrom: parseFloat(minPrice),
          dailyPriceTo: parseFloat(maxPrice),
          areaIds: isNaN(selectedCities?.[0]) ? [] : selectedCities,
          branchIds: branches,
          allyCompanyIds: allyIds,
          extraServiceIds: ExtraService,
          vehicleTypeIds: vehicleTypes,
          carVersionIds: versions,
          isDeepLink: deeplink,
        },
      }).then(() => {
        NotificationManager.success(<FormattedMessage id="bannereditedsuccessfully" />);
        refetch();
        history.push("/cw/dashboard/banners");
      });
      return;
    }
    const geterrors = {};
    if (!ArImage) {
      geterrors.ArImage = "thisfieldisrequired";
    }
    if (!EnImage) {
      geterrors.EnImage = "thisfieldisrequired";
    }
    if (!displayOrder) {
      geterrors.displayOrder = "thisfieldisrequired";
    }

    if (!Object.keys(geterrors).length) {
      createBanner({
        variables: {
          displayOrder,
          imgAr: ArImage,
          imgEn: EnImage,
          isActive: isActive?.value != "false",
          dailyPriceFrom: parseFloat(minPrice),
          dailyPriceTo: parseFloat(maxPrice),
          areaIds: isNaN(selectedCities?.[0]) ? [] : selectedCities,
          branchIds: branches,
          allyCompanyIds: allyIds,
          extraServiceIds: ExtraService,
          vehicleTypeIds: vehicleTypes,
          carVersionIds: versions,
          isDeepLink: deeplink,
        },
      })
        .then((res) => {
          allBanners();
        })
        .then(() => {
          NotificationManager.success(<FormattedMessage id="BannerAddSuccessfully" />);
          history.push("/cw/dashboard/banners");
        })
        .catch((error) => NotificationManager.error(error));
    } else {
      setErrors(geterrors);
    }
  };

  const uploadEnimage = (file) => {
    setChanged(true);
    setLoader(true);
    imageUpload({
      variables: {
        image: file,
        topic: "English Banner  Image ",
      },
    }).then((res) => {
      setLoader(false);
      setEnImage(res.data.imageUpload.imageUpload.imageUrl);
    });
  };
  const uploadArimage = (file) => {
    setChanged(true);
    setArLoader(true);
    imageUpload({
      variables: {
        image: file,
        topic: "Arabic Banner  Image ",
      },
    }).then((res) => {
      setArLoader(false);

      setArImage(res.data.imageUpload.imageUpload.imageUrl);
    });
  };
  const handelChange = (e) => {
    setChanged(true);
    setDisplayOrder(+e.target.value);
  };
  const shareHandler = (bannerId) => {
    const url = `https://carwah.com?bannerId=${bannerId}`;
    navigator.clipboard.writeText(url).then(() => {
      swal({
        title: formatMessage({ id: "Link Copied" }),
        icon: "success",
      });
    });
  };
  useEffect(() => {
    if (banner) {
      if (banner?.banner?.isActive) {
        setIsActive({ label: formatMessage({ id: "active" }), value: banner?.banner?.isActive });
      } else {
        setIsActive({ label: formatMessage({ id: "inactive" }), value: banner?.banner?.isActive });
      }
      setDeepLink(banner?.banner?.isDeepLink);
      setSelectedExtraService(banner?.banner?.extraServiceIds?.map((id) => +id));
      setSelectedVehicle(banner?.banner?.vehicleTypeIds?.map((id) => +id));
      setSelectedBranches(banner?.banner?.branchIds?.map((id) => +id));
      setMinPrice(banner?.banner?.dailyPriceFrom);
      setMaxPrice(banner?.banner?.dailyPriceTo);
      setAllyIds(banner?.banner?.allyCompanyIds?.map((id) => +id));
      setSelectedCity(banner?.banner?.areaIds?.map((id) => +id));
      SetSelectedVersions(banner?.banner?.carVersionIds?.map((id) => +id));
      setDisplayOrder(banner?.banner?.displayOrder);
      setEnImage(banner?.banner?.imgEn);
      setArImage(banner?.banner?.imgAr);
    }
  }, [banner]);
  return (
    <div className="clients-wrapper">
      <Helmet>
        <title>
          {formatMessage({ id: bannerId ? "sidebar.EditBanner" : "sidebar.AddBanner" })}
        </title>
      </Helmet>

      <PageTitleBar
        title={<IntlMessages id={bannerId ? "sidebar.EditBanner" : "sidebar.AddBanner"} />}
        match={location}
        lastElement={bannerId ? bannerId : <IntlMessages id={"sidebar.AddBanner"} />}
        enableBreadCrumb
      />
      <form
        onSubmit={(e) => {
          e.preventDefault();
        }}
        autoComplete="off"
      >
        <div className="row">
          <div className="col-md-12 d-flex align-items-center mb-2" style={{ gap: "20px" }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={deeplink}
                  onChange={(e) => {
                    setChanged(true);
                    setDeepLink(e.target.checked);
                  }}
                />
              }
              label={<FormattedMessage id="DeepLink" />}
            />
            {deeplink && bannerId ? (
              <Tooltip title={formatMessage({ id: "copyBannerLink" })} placement="top">
                <div
                  onClick={() => shareHandler(bannerId)}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    cursor: "pointer",
                    gap: "5px",
                    marginTop: "-7px",
                  }}
                >
                  <i class="ti-files" aria-hidden="true"></i>
                  <span>{formatMessage({ id: "copyBannerLink" })}</span>
                </div>
              </Tooltip>
            ) : null}
          </div>
          {((bannerId && !BannerLoader) || !bannerId) && (
            <div className="col-md-6">
              <CustomTextField
                fullWidth
                onChange={handelChange}
                value={displayOrder}
                type="number"
                min={0}
                required
                name="sortorder"
                error={errors?.displayOrder}
              />
              {errors?.displayOrder && (
                <helperText className="text-danger">
                  <FormattedMessage id="thisfieldisrequired" />
                </helperText>
              )}
            </div>
          )}
          <div className="col-md-6">
            <Select
              className="dropdown-select mb-4"
              options={BannerStatus(formatMessage)}
              placeholder={formatMessage({ id: "status" })}
              onChange={(selection) => {
                selection && setChanged(true);
                setIsActive(selection);
              }}
              value={isActive}
            />
          </div>
        </div>
        {deeplink ? (
          <>
            <div className="row mt-2 mb-4">
              <div className="col-md-6">
                {
                  <CitiesDropDown
                    valueAttribute="id"
                    // multiple
                    selectedCity={selectedCities}
                    setSelectedCity={(_cities) => {
                      if (!_cities) {
                        setSelectedCity([]);
                        setChanged(true);
                        return;
                      }
                      const cities = [_cities];
                      setChanged(true);
                      setSelectedCity(cities?.map((item) => +item.id));
                      cities?.length && setChanged(true);
                    }}
                  />
                }
              </div>
              <div className="col-md-6">
                <AllyName
                  valueAttribute="id"
                  selectedAlly={allyIds}
                  multiple={true}
                  onAllyChange={() => console.log("")}
                  cities={selectedCities}
                  setSelectedAlly={(ally) => {
                    setChanged(true);
                    setAllyIds(ally?.map((item) => +item.value));
                    ally?.length && setChanged(true);
                  }}
                />
              </div>
            </div>
            <div className="row mt-2 mb-4">
              <div className="col-md-6">
                <BranchesDropDown
                  allyId={allyIds}
                  banner
                  valueAttribute="id"
                  selectedBranch={branches}
                  multiple={true}
                  cities={isNaN(selectedCities?.[0]) ? [] : selectedCities}
                  setSelectedBranch={(Id) => {
                    setChanged(true);
                    Id && setChanged(true);
                    setSelectedBranches(Id);
                  }}
                />
              </div>
              <div className="col-md-6">
                <CarVersionsDropDown
                  valueAttribute="id"
                  setSelectedVersion={(Id) => {
                    setChanged(true);
                    Id && setChanged(true);
                    SetSelectedVersions(Id);
                  }}
                  selectVersion={versions}
                  multiple={true}
                />
              </div>
            </div>
            <div className="row mt-2 mb-2">
              <div className="col-md-6 mt-1">
                <VehicleTypeDropDown
                  valueAttribute="id"
                  label="carType"
                  multi={true}
                  selectedVehicle={vehicleTypes}
                  setSelectedVehicle={(Id) => {
                    setChanged(true);
                    setSelectedVehicle(Id);
                  }}
                />
              </div>
              <div className="col-md-6">
                <ExtraServiceDropDown
                  setSelectedExtraService={(Id) => {
                    setChanged(true);
                    setSelectedExtraService(Id);
                  }}
                  selectedExtraService={ExtraService}
                  multiple
                />
              </div>
            </div>
            <RentalPriceFilter
              setMinPrice={setMinPrice}
              setMaxPrice={setMaxPrice}
              minPrice={minPrice}
              maxPrice={maxPrice}
              action={() => {
                setChanged(true);
              }}
            />
          </>
        ) : null}

        <div className="row mt-3">
          <div className="col-md-6">
            <FileUploader
              loader={loader}
              titleId="EnImage"
              image={EnImage}
              required
              setImage={(file) => {
                uploadEnimage(file);
              }}
            />
            {errors?.EnImage && (
              <helperText className="text-danger">
                <FormattedMessage id="thisfieldisrequired" />
              </helperText>
            )}
          </div>
          <div className="col-md-6">
            <FileUploader
              titleId="ArImage"
              loader={Arloader}
              image={ArImage}
              required
              setImage={(file) => {
                uploadArimage(file);
              }}
            />
            {errors?.EnImage && (
              <helperText className="text-danger">
                <FormattedMessage id="thisfieldisrequired" />
              </helperText>
            )}
          </div>
        </div>
        <div className="row justify-content-end">
          <div className="col-md-6">
            <div className="row justify-content-center">
              <div className="col-md-3 mt-2">
                <button
                  type="button"
                  onClick={() => handelSaveBanner()}
                  className="btn btn-primary text-center text-white"
                  disabled={CreateLoader || updateLoader || !changed}
                >
                  <FormattedMessage id="save" />
                </button>{" "}
              </div>
              <div className="col-md-3 mt-2">
                <button
                  onClick={() => history.goBack()}
                  type="button"
                  className="btn btn-danger text-white text-center"
                >
                  <FormattedMessage id="cancel" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
