import { userCan } from "functions";
import React, { useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { useMutation, gql } from "@apollo/client";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
  Tooltip,
  Snackbar,
} from "@material-ui/core";
import { Link as LinkIcon, FileCopy as FileCopyIcon } from "@material-ui/icons";
import MuiAlert from "@material-ui/lab/Alert";
import PropTypes from "prop-types";
import WhatsAppIcon from '@material-ui/icons/WhatsApp';
import EmailIcon from '@material-ui/icons/Email';
import SmsIcon from '@material-ui/icons/Sms';
import {PaymentLinkNotification} from "gql/mutations/PaymentLink.gql"
import { NotificationManager } from "react-notifications";
const GENERATE_PAYMENT_LINK = gql`
  mutation GeneratePaymentLink($payableId: ID!, $payableType: Payable!) {
    generatePaymentLink(payableId: $payableId, payableType: $payableType) {
      errors
      paymentLink
    }
  }
`;

function Alert(props) {
  return <MuiAlert elevation={6} variant="filled" {...props} />;
}

function PaymentLink({ payableId, payableType, hide, token, validForPayment }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [paymentLink, setPaymentLink] = useState(token || "");
  const [snackbar, setSnackbar] = useState({ open: false, message: "", severity: "success" });
  const { formatMessage } = useIntl();
  const [paymentLinkNotification]=useMutation(PaymentLinkNotification)
  const [generateLink, { loading }] = useMutation(GENERATE_PAYMENT_LINK, {
    onCompleted: (data) => {
      if (data.generatePaymentLink.errors?.length) {
        setSnackbar({
          open: true,
          message: data.generatePaymentLink.errors[0],
          severity: "error",
        });
        return;
      }
      setPaymentLink(data.generatePaymentLink.paymentLink);
      setIsModalOpen(true);
    },
    onError: (error) => {
      setSnackbar({
        open: true,
        message: error.message,
        severity: "error",
      });
    },
  });

  const handleGenerateLink = () => {
   
      generateLink({
        variables: {
          payableId,
          payableType,
        },
      });
    
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(paymentLink);
      setSnackbar({
        open: true,
        message: formatMessage({ id: "copied.to.clipboard" }),
        severity: "success",
      });
    } catch (err) {
      setSnackbar({
        open: true,
        message: formatMessage({ id: "copy.failed" }),
        severity: "error",
      });
    }
  };

  if (!userCan("rentals.generate_payment_link") || hide) return null;
const handelClickIcon=(type)=>{
  const params = new URLSearchParams(new URL(paymentLink).search);
  const token1 = params.get("token");
  paymentLinkNotification({
    variables:{
      token:token1,
      channels:[type]
    }
  }).then((data)=>{
    if(data?.data?.paymentLinkNotification?.success){
                    NotificationManager.success(<FormattedMessage id="Shared Successfully" />);
      

    }else{
      NotificationManager.error(<FormattedMessage id="something went wrong " />);
      
    }
  })
}
  return (
    <>
      <Tooltip title={formatMessage({ id: "generate.payment.link" })}>
        <span>
          <IconButton onClick={handleGenerateLink} disabled={loading} color="primary" size="small">
            <LinkIcon />
          </IconButton>
        </span>
      </Tooltip>

      <Dialog open={isModalOpen} onClose={() => setIsModalOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle>
          <FormattedMessage id="payment.link" />
        </DialogTitle>
        <DialogContent>
          <div style={{ wordBreak: "break-all", marginTop: 8, marginBottom: 12 }}>
            {paymentLink}
          </div>
          <div className="d-flex gap-5">
            <div>
              <p>
                <FormattedMessage id="share via"/>
              </p>
            </div>
            <div style={{minWidth:"200px",display:"flex",justifyContent:"space-evenly"}}>
            <Tooltip title={formatMessage({ id: "whatsapp" })}>

              <WhatsAppIcon  onClick={()=>handelClickIcon("WHATSAPP")} style={{cursor:"pointer"}}/>
                </Tooltip>
            <Tooltip title={formatMessage({ id: "email" })}>

              <EmailIcon  onClick={()=>handelClickIcon("EMAIL")} style={{cursor:"pointer"}}/>
                </Tooltip>
            <Tooltip title={formatMessage({ id: "sms" })}>
              <SmsIcon onClick={()=>handelClickIcon("SMS")} style={{cursor:"pointer"}}/>
                </Tooltip>
            </div>
          </div>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsModalOpen(false)}>
            <FormattedMessage id="close" />
          </Button>
          <Button
            color="primary"
            variant="contained"
            startIcon={<FileCopyIcon style={{marginInline:"6px"}}/>}
            onClick={copyToClipboard}
          >
            <FormattedMessage id="copy.link" />
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
}

PaymentLink.propTypes = {
  payableId: PropTypes.string.isRequired,
  payableType: PropTypes.oneOf(["Installment", "Rental", "RentalDateExtensionRequest"]).isRequired,
  hide: PropTypes.bool,
  token: PropTypes.string,
  validForPayment: PropTypes.bool,
};

export default PaymentLink;
