
query Feedbacks ($limit:Int,$page:Int,$customer:String,$id:Int,$status:FeedbackStatus,$booking:String,$feedbackType:FeedbackType,$feedbackAbout:FeedbackAbout,$feedbackTopicId:ID ){
  feedbacks(limit: $limit, page: $page,customer:$customer,id:$id,status:$status,booking:$booking,feedbackType:$feedbackType,feedbackAbout:$feedbackAbout,feedbackTopicId:$feedbackTopicId) {
    collection {
      accountFullName
      accountIban
      attachment
      description
      feedbackAbout
      feedbackType
      rental{
        id
        bookingNo
      }
      id
      createdAt
      resolvingDate
      status
      topicReason
    }
    
    metadata {
      currentPage
      limitValue
      totalCount
      totalPages
    }
  }
}
