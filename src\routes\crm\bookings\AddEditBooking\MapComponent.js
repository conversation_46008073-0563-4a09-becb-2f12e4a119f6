import React from "react"
import Map from "components/Map/MapWithSearch";

const MapComponent =({deliverLat,deliverLng,setMapChange,mapChange,setDeliverAddress})=>{
    return(
        <Map
        latitude={deliverLat}
        longitude={deliverLng}
        setLatitude={(lat) => setDeliverLat(lat)}
        setLongitude={(lng) => setDeliverLng(lng)}
        centerlat={deliverLat}
        centerlng={deliverLng}
        setMapChange={setMapChange}
        mapChange={mapChange}
        isBooking
        setDeliverAddress={setDeliverAddress}
      />
    )
}
export default MapComponent