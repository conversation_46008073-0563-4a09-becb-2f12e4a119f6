/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
import React from "react";
import ExtenstionRequests from "./extensionRequests";
import TimeLine from "../components/TimeLineModal";
import AllyRentalRejections from "./AllyRentalRejectionsReasons";

function Modals({
  extensionModalOpen,
  setIsExtensionModalOpen,
  rentalDetails,
  refetchBooking,
  opneTimeLineModal,
  setOpenTimeLineModal,
  bookingId,
  OpenRejectionModal,
  setOpenRejectionModal,
  company
}) {
  return (
    <>
      {extensionModalOpen && (
        <ExtenstionRequests
          extensionModalOpen={extensionModalOpen}
          setIsExtensionModalOpen={setIsExtensionModalOpen}
          rentalDetails={rentalDetails?.rentalDetails}
          rentalDateExtensionRequests={rentalDetails?.rentalDetails?.rentalDateExtensionRequests}
          hasPendingExtensionRequests={rentalDetails?.rentalDetails?.hasPendingExtensionRequests}
          refetchBooking={refetchBooking}
          company={company}
        />
      )}
      {opneTimeLineModal && (
        <TimeLine
          isOpen={opneTimeLineModal}
          setOpenTimeLineModal={setOpenTimeLineModal}
          BookingId={bookingId}
        />
      )}
      {OpenRejectionModal && (
        <AllyRentalRejections
          rentalDetails={rentalDetails}
          OpenRejectionModal={OpenRejectionModal}
          setOpenRejectionModal={setOpenRejectionModal}
        />
      )}
    </>
  );
}

export default Modals;
