/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
/* eslint-disable no-nested-ternary */
/**
 * Booking Info Widget
 */
import React from "react";
import PropTypes from "prop-types";
import { List, ListItem } from "@material-ui/core";
import IntlMessages from "util/IntlMessages";
import { FormattedMessage, useIntl } from "react-intl";
import ModalImage from "react-modal-image";
import moment from "moment";
import RepliesCard from "./RepliesCard";

export default function ListedInformation({
  data,
  features,
  branches,
  areas,
  allyCompanies,
  agencyCustomerProfiles,
  rejectReasones,
  RentalContract,
  notes,
  plans,
  closingReasons,
  cancelledReason,
  AllyrejectReason,
  replies,
}) {
  const { locale } = useIntl();
  return (
    <List className="fs-14 p-2">
      {data?.length > 0 &&
        data.map((info, index) =>
          info?.image ? (
            <React.Fragment key={JSON.stringify(index)}>
              <div className="text-center">{info?.id && <FormattedMessage id={info.id} />}</div>
              <div
                key={JSON.stringify(index)}
                className={`${info.imageDetails.containerClassName} d-flex justify-content-center mt-1 modal-img`}
                style={{ marginBottom: "20px" }}
              >
                <ModalImage
                  key={JSON.stringify(index)}
                  small={info.image}
                  large={info.image}
                  alt={info.image}
                  className={`${info.imageDetails.className} `}
                />
              </div>
            </React.Fragment>
          ) : (
            <>
              {info?.value ? (
                <ListItem
                  key={JSON.stringify(index)}
                  data-testid={`data-info-${index}`}
                  className="d-flex justify-content-between align-items-center list_item_info"
                  style={{ padding: "7px 20px"}}
                >
                  <span className="text-align-localized" >
                    {info?.icon}
                    {info?.msgId && <IntlMessages id={info?.msgId} />}
                  </span>
                  <span style={{color: info?.color ? info?.color : "",direction:info?.msgId =="Phone"? "ltr" :  ""}}>
                    {info && info?.value}{" "}
                    {info?.msgId == "Earning Value" ? <FormattedMessage id="miles" /> : ""}{" "}
                  </span>
                </ListItem>
              ) : null}
            </>
          ),
        )}
      {notes && data?.length
        ? data?.map((item, index) => (
            <ListItem
              key={JSON.stringify(index)}
              data-testid={`data-info-${index}`}
              className="d-flex justify-content-between align-items-center"
              style={{ padding: "7px 20px" }}
            >
              <span>{item.note}</span>
              <span>{typeof item?.status === "string" ? item.status : null}</span>
            </ListItem>
          ))
        : null}
      {rejectReasones &&
        rejectReasones?.length &&
        rejectReasones.map((rejectreason, index) => (
          <ListItem
            key={JSON.stringify(index)}
            data-testid={`data-info-${index}`}
            className="d-flex justify-content-between align-items-center"
            style={{ padding: "7px 20px" }}
          >
            <span className="text-align-localized">
              <IntlMessages id="Decline.reason" />
            </span>
            <span>{rejectreason[`${locale}Body`]}</span>
          </ListItem>
        ))}
      {RentalContract && (
        <>
          <ListItem
            data-testid="data-info"
            className="d-flex justify-content-between align-items-center"
            style={{ padding: "7px 20px" }}
          >
            <span>
              <IntlMessages id="action.status" />
            </span>
            <span>{RentalContract?.contractNo}</span>
          </ListItem>
          <ListItem
            data-testid="data-info"
            className="d-flex justify-content-between align-items-center"
            style={{ padding: "7px 20px" }}
          >
            <span>
              <IntlMessages id="refund.amount" />
            </span>
            <span>{RentalContract?.refundAmount}</span>
          </ListItem>
          <ListItem
            data-testid="data-info"
            className="d-flex justify-content-between align-items-center"
            style={{ padding: "7px 20px" }}
          >
            <span>
              <IntlMessages id="operation.datetime" />
            </span>
            <span>{RentalContract?.operationDateTime}</span>
          </ListItem>
          <ListItem
            data-testid="data-info"
            className="d-flex justify-content-between align-items-center"
            style={{ padding: "7px 20px" }}
          >
            <span>
              <IntlMessages id="operationType" />
            </span>
            <span>{RentalContract?.operationType}</span>
          </ListItem>
          <ListItem
            data-testid="data-info"
            className="d-flex justify-content-between align-items-center"
            style={{ padding: "7px 20px" }}
          >
            <span>
              <IntlMessages id="createdat" />
            </span>
            <span>{RentalContract?.createdAt}</span>
          </ListItem>
        </>
      )}
      {features && (
        <ListItem
          className="d-flex justify-content-between align-items-center"
          style={{ padding: "7px 20px" }}
        >
          <span>
            <FormattedMessage id="CarFeatures" />
          </span>
          <span>
            {features &&
              features.map((feature) => (
                <span className="badge badge-primary mr-2">
                  {feature[`name${locale.charAt(0).toUpperCase() + locale.charAt(1)}`]}
                </span>
              ))}
          </span>
        </ListItem>
      )}
      {plans && (
        <ul style={{ listStyleType: "none", paddingInline: "20px" }}>
          <li>
            <FormattedMessage id="plan" />
            <ul style={{ paddingInline: "100px", lineHeight: 2 }}>
              <li>
                <span>
                  <FormattedMessage id="No. of months" />
                </span>
                {" :         "}
                <span>{plans?.noOfMonths}</span>
              </li>

              <li>
                <span>
                  <FormattedMessage id="1st Installment" />
                </span>
                {" :          "}
                <span>{plans?.firstInstallment}</span>
              </li>

              <li>
                <span>
                  <FormattedMessage id="Monthly Installment" />
                </span>
                {" :             "}
                <span>{plans?.monthlyInstallment}</span>
              </li>
              <li>
                <span>
                  <FormattedMessage id="Final Installment" />
                </span>
                {" :            "}
                <span>{plans?.finalInstallment}</span>
              </li>
            </ul>
          </li>
        </ul>
      )}
      {AllyrejectReason && (
        <ul style={{ listStyleType: "none", paddingInline: "20px" }}>
          <li>
            <FormattedMessage id="Decline Reason" />
            <ul style={{ paddingInline: "100px", lineHeight: 2 }}>
              {AllyrejectReason?.length
                ? AllyrejectReason?.map((reason) => (
                    <li>
                      <span>{reason?.body}</span>
                    </li>
                  ))
                : null}
            </ul>
          </li>
        </ul>
      )}
      {closingReasons && closingReasons?.length ? (
        <ul style={{ listStyleType: "none", paddingInline: "20px" }}>
          <li>
            <FormattedMessage id="closingReasons" />
            <ul style={{ paddingInline: "100px", lineHeight: 2 }}>
              {closingReasons?.length && cancelledReason
                ? [...closingReasons, cancelledReason]?.map((reason) => (
                    <li>
                      <span>{reason}</span>
                    </li>
                  ))
                : closingReasons?.map((reason) => (
                    <li>
                      <span>{reason}</span>
                    </li>
                  ))}
            </ul>
          </li>
        </ul>
      ) : null}
      {areas && (
        <ListItem
          className="d-flex justify-content-between align-items-center"
          style={{ padding: "7px 20px" }}
        >
          <span>
            <FormattedMessage id="City" />
          </span>
          <span>
            {areas && areas.length ? (
              areas.map((area) => (
                <span className="badge badge-primary mr-2">{area[`${locale}Name`]}</span>
              ))
            ) : (
              <FormattedMessage id="all" />
            )}
          </span>
        </ListItem>
      )}
      {allyCompanies && (
        <ListItem
          className="d-flex justify-content-between align-items-center"
          style={{ padding: "7px 20px" }}
        >
          <span>
            <FormattedMessage id="Ally" />
          </span>
          <span>
            {allyCompanies && allyCompanies.length ? (
              allyCompanies.map((company) => (
                <span className="badge badge-primary mr-2">{company[`${locale}Name`]}</span>
              ))
            ) : (
              <FormattedMessage id="all" />
            )}
          </span>
        </ListItem>
      )}
       {agencyCustomerProfiles && (
        <ListItem
          className="d-flex justify-content-between align-items-center"
          style={{ padding: "7px 20px" }}
        >
          <span>
            <FormattedMessage id="Agencies" />
          </span>
          <span>
            {agencyCustomerProfiles && agencyCustomerProfiles.length ? (
              agencyCustomerProfiles.map((agencyprofile) => (
                <span className="badge badge-primary mr-2">{agencyprofile?.agency?.name}</span>
              ))
            ) : (
              <FormattedMessage id="all" />
            )}
          </span>
        </ListItem>
      )}

      {branches?.length ? (
        <ListItem
          className="d-flex justify-content-between align-items-center "
          style={{ padding: "7px 20px" }}
        >
          <span>
            <FormattedMessage id="branches" />
          </span>
          <span>
            {branches &&
              branches?.map((branch) => (
                <span className="badge badge-danger mr-2">{branch[`${locale}Name`]}</span>
              ))}
          </span>
        </ListItem>
      ) : branches ? (
        <ListItem
          className="d-flex justify-content-between align-items-center "
          style={{ padding: "7px 20px" }}
        >
          <span>
            <FormattedMessage id="branches" />
          </span>
          <span>
            <FormattedMessage id="all" />
          </span>
        </ListItem>
      ) : null}
    </List>
  );
}

ListedInformation.propTypes = {
  data: PropTypes.any,
};
