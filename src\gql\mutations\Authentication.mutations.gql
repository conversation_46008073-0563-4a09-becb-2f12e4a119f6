mutation LoginDashboardMutation($email: String!, $password: String!) {
  loginDashboard(input: { email: $email, password: $password }) {
    token
    errors
    status
  }
}
mutation AgencyDashboardLoginMutation($email: String!, $password: String!, $agencyKey: String!) {
  agencyDashboardLogin(input: { email: $email, password: $password, agencyKey: $agencyKey }) {
    token
    errors
    status
  }
}

mutation LogoutMutation($clientMutationId: String!) {
  logout(input: { clientMutationId: $clientMutationId }) {
    errors
    status
  }
}
