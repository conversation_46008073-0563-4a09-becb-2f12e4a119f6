/**
 * Charts Widgets Page
 */
import React, { Component, useEffect, useState } from "react";

import {
  DailySales,
  CampaignPerformance,
  TrafficChannel,
  EmailStatisticsVersion2Widget,
  TotalEarnsChartWidget,
  BandWidthAreaChartWidget,
  BandWidthUsageBarChartWidget,
  TotalEarnsWithAreaChartWidget,
  ProductStatsWidget,
  EmailStaticsWidget,
  RevenueWidget,
  OnlineVisitorsWidget,
  TrafficSourcesWidget,
  BandwidthUsageWidget,
  SiteVisitorChartWidget,
} from "Components/Widgets";

// page title bar
import PageTitleBar from "Components/PageTitleBar/PageTitleBar";

// intl messages
import IntlMessages from "Util/IntlMessages";

// rct collapsible card
import RctCollapsibleCard from "components/RctCollapsibleCard";

// widgets data
import {
  emailStatisticsData,
  dataUsed,
  productStats,
  emailStatisticsV2Data,
  totalRevenue,
  onlineVisitorsData,
  trafficSources,
  totalEarnsV2,
  dailySales,
  trafficChannel,
  siteVisitors,
  totalEarns,
} from "../../widgets/data";
import { useQuery } from "@apollo/client";

import { CustomerStatistics } from "gql/queries/UsersPerMonth.gql";
import { FormattedMessage, useIntl } from "react-intl";
import { Helmet } from "react-helmet";
import store from "../../../store";

const StatisticsChart = (props) => {
  const { formatMessage, locale } = useIntl();
  const { is_super_user, ally_id } = store.getState()?.authUser.user;

  const { data: customerStatistics } = useQuery(CustomerStatistics);

  // const [userStatistics,setUserStatistics] =useState(customerStatistics?.customerStatistics?.map((item)=>(

  //     { name: item.month, uv: item.count}

  // )))
  const [userStatistics, setUserStatistics] = useState([]);
  useEffect(() => {
    if (customerStatistics) {
      const data = customerStatistics?.customerStatistics?.map((item) =>
        locale == "ar"
          ? { name: item.month, uv: item.count, "أجمالي المستخدمين": item.count }
          : { name: item.month, uv: item.count, "Total Users": item.count },
      );
      setUserStatistics(data);
    }
  }, [customerStatistics]);

  return (
    <div className="charts-widgets-wrapper">
      <Helmet>
        <title>{formatMessage({ id: "sidebar.statistics" })}</title>
      </Helmet>
      <PageTitleBar title={<IntlMessages id="sidebar.statistics" />} match={props.match} />
      <RctCollapsibleCard heading={<IntlMessages id="rentals" />} collapsible>
        <TotalEarnsWithAreaChartWidget height={400} chartData={totalEarns} />
      </RctCollapsibleCard>

      <div className="dash-cards-lg">
        <div className="row">
          <div className="col-sm-6 col-md-6 col-xl-6 w-xs-full-block">
            <RevenueWidget data={totalRevenue} />
          </div>
          {!ally_id ? (
            <RctCollapsibleCard
              colClasses="col-sm-6 col-md-6 w-xs-full"
              heading={<FormattedMessage id="users.per.month" />}
              collapsible
              fullBlock
            >
              {userStatistics?.length && <SiteVisitorChartWidget data={userStatistics} />}
            </RctCollapsibleCard>
          ) : null}

          {/* <div className="col-sm-6 col-md-3 col-xl-3 w-xs-half-block">
              <OnlineVisitorsWidget data={onlineVisitorsData} />
            </div> */}
          {/* <div className="col-sm-6 col-md-3 col-xl-3 w-xs-half-block">
              <TrafficSourcesWidget data={trafficSources} />
            </div> */}
          {/* <div className="col-sm-6 col-md-3 col-xl-3 w-xs-half-block">
              <BandwidthUsageWidget />
            </div> */}
        </div>
      </div>

      {/* <div className="row">
          <div className="col-sm-12 col-md-6">
            <BandWidthAreaChartWidget />
          </div>
          <div className="col-sm-12 col-md-6">
            <BandWidthUsageBarChartWidget data={dataUsed} />
          </div>
        </div> */}
      {/* <div className="row">
          <RctCollapsibleCard
            colClasses="col-md-7 col-xl-8 w-xs-half-block w-8-full"
            heading={<IntlMessages id="widgets.productStats" />}
            collapsible
            reloadable
            closeable
          >
            <ProductStatsWidget data={productStats} />
          </RctCollapsibleCard>
          <RctCollapsibleCard
            customClasses="gradient-primary"
            colClasses="col-md-5 col-xl-4 w-xs-half-block w-8-full"
            heading={<IntlMessages id="widgets.emailsStatistics" />}
            collapsible
            reloadable
            closeable
            fullBlock
          >
            <EmailStaticsWidget
              openChartData={emailStatisticsV2Data.chartData.open}
              bounceChartData={emailStatisticsV2Data.chartData.bounce}
              unsubscribeData={emailStatisticsV2Data.chartData.unsubscribe}
            />
          </RctCollapsibleCard>
        </div> */}
      <div className="row"></div>
    </div>
  );
};
export default StatisticsChart;
