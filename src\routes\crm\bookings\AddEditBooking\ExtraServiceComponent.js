import React from "react"
import { FormattedMessage, useIntl } from "react-intl";

const ExtraServiceComponent =({selectedBranch,selectedCar,unLimited,setChanged,rentalDetails,branchExtraServicesIds,setBranchExtraServicesIds,setAllyExtraServicesIds,allyExtraServicesIds,locale,bookingId })=>{
   return(
    <>
    <h4 className="mt-4 mb-4">
      <FormattedMessage id="rental.extraServices" />
    </h4>
    <div className="row mb-4" style={{ gap: "5px" }}>
      {selectedBranch?.allyExtraServicesForAlly?.map((i) => (
        <div
          key={i.id}
          className="d-flex "
          style={{ gap: "5px" }}
          onClick={() => setChanged(true)}
        >
          <div className="">
            <input
              id={`ally-extra-${i.id}`}
              style={{ cursor: "pointer" }}
              type="checkbox"
              defaultChecked={
                i.isRequired ||
                rentalDetails?.rentalDetails?.rentalExtraServices
                  ?.filter((s) => s.extraServiceType === "ally_company")
                  ?.find((item) => +item.extraServiceId === +i.id) ||
                allyExtraServicesIds.includes(i.id)
              }
              disabled={i.isRequired}
              onChange={(e) => {
                e.target.checked
                  ? setAllyExtraServicesIds([...allyExtraServicesIds, i.id])
                  : setAllyExtraServicesIds(
                      allyExtraServicesIds.filter(
                        (item) => +item !== +i.id,
                      ),
                    );
              }}
            />
          </div>
          <label
            htmlFor={`ally-extra-${i.id}`}
            style={{ cursor: "pointer", margin: 0 }}
          >
            <h5 style={{ fontSize: "14px" }}>
              {i.extraService[`${locale}Title`]}
            </h5>
            <h6 style={{ fontSize: "12px" }}>{i.subtitle}</h6>
          </label>
        </div>
      ))}
      {(!bookingId && selectedCar.isUnlimited) ||
      (bookingId &&
        rentalDetails?.rentalDetails &&
        selectedCar.isUnlimited) ? (
        <div className="d-flex">
          <input
            type="checkbox"
            checked={unLimited}
            style={{ height: "19px" }}
            id="Unlimited.KM"
            onChange={(e) => setunLimited(e.target.checked)}
          />
          <label
            htmlFor="Unlimited.KM"
            style={{ cursor: "pointer", margin: 0 }}
          >
            <h5 style={{ fontSize: "14px", margin: 0 }}>
              <FormattedMessage id="Unlimited.KM" />
            </h5>
            <h6 style={{ fontSize: "12px" }}>
              <FormattedMessage
                id="price.sr/day"
                values={{ price: selectedCar.unlimitedFeePerDay }}
              />
            </h6>
          </label>
        </div>
      ) : null}

      {selectedBranch?.branchExtraServices?.map((i) => (
        <div
          key={i.id}
          className="d-flex"
          style={{ gap: "5px" }}
          onClick={() => setChanged(true)}
        >
          <input
            id={`branch-extra-${i.id}`}
            style={{ cursor: "pointer", height: "19px" }}
            type="checkbox"
            defaultChecked={
              i.isRequired ||
              rentalDetails?.rentalDetails?.rentalExtraServices
                ?.filter((s) => s.extraServiceType === "branch")
                ?.find((item) => +item.extraServiceId === +i.id) ||
              branchExtraServicesIds.includes(i.id)
            }
            disabled={i.isRequired}
            onChange={(e) => {
              e.target.checked
                ? setBranchExtraServicesIds([
                    ...branchExtraServicesIds,
                    i.id,
                  ])
                : setBranchExtraServicesIds(
                    branchExtraServicesIds.filter(
                      (item) => +item !== +i.id,
                    ),
                  );
            }}
          />

          <label
            htmlFor={`branch-extra-${i.id}`}
            style={{ cursor: "pointer", margin: 0 }}
          >
            <h5 style={{ fontSize: "14px" }}>
              {i.allyExtraService?.extraService[`${locale}Title`]}
            </h5>
            <h6 style={{ fontSize: "12px" }}>{i.subtitle}</h6>
          </label>
        </div>
      ))}
      {Boolean(selectedBranch?.canDelivery) && (
        <div className="d-flex" style={{ gap: "5px" }}>
          <input
            id="delivery"
            style={{ cursor: "pointer", height: "19px" }}
            type="checkbox"
            checked={isDelivery}
            onChange={(e) => {
              setChanged(true);
              setIsDelivery(e.target.checked);
            }}
          />

          <label
            htmlFor="delivery"
            style={{ cursor: "pointer", margin: 0 }}
          >
            <h5 style={{ fontSize: "14px" }}>
              <FormattedMessage id="delivery" />
            </h5>
          </label>
        </div>
      )}
    </div>
  </>
   )
}
export default ExtraServiceComponent