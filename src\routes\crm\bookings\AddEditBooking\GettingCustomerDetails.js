import React, { useState, useEffect } from "react";
import IntlTelInput from "react-intl-tel-input";
import PropTypes from "prop-types";
import { FormattedMessage } from "react-intl";
import { useQuery } from "@apollo/client";
import { GetUsers } from "gql/queries/Users.queries.gql";
import { NotificationManager } from "react-notifications";
import { DangerFormattedMessage } from "components/Danger/Danger";
import FullPageLogoLoader from "components/shared/FullPageLogoLoader";
import {AgencyCustomersQuery} from "gql/queries/Agencies.gql"
import Select from "react-select";

function GettingCustomerDetails({
  setCustomerDetails,
  customerId,
  setCustomerCanBook,
  setFusranChecked,
  setFursanVerified,
  setCustomerId,
}) {
  const [customerNumber, setCustomerNumber] = useState("");
  const [isCustomerProfileCompleted, setIsCustomerProfileCompleted] = useState(true);
  const [fullNumber, setFullNumber] = useState("");
  const [isNumberValid, setIsNumberValid] = useState(true);
  const [requestCustomerDetails, setRequestCustomerDetails] = useState(false);
  const user_data = JSON.parse(localStorage.getItem("user_data"));
  const isAgency = user_data?.agency_id;
const[SelectedCustomer,setSelectedCustomer]=useState(false)
  const { data: customerDetailsRes, loading } = useQuery(GetUsers, {
    skip: !requestCustomerDetails,
    fetchPolicy: "no-cache",
    variables: { mobile: fullNumber, type: "customers",agencyId:isAgency },
  });
  const {data:agencyCustomersQuery} =useQuery(AgencyCustomersQuery,{
    fetchPolicy: "no-cache",
    variables: { isActive:true,agencyId:isAgency },
  })

  useEffect(() => setRequestCustomerDetails(false), [customerNumber]);

  useEffect(() => {
    if (customerDetailsRes) {
      const details = customerDetailsRes?.users?.collection?.[0] || null;
      if (details) {
        setFusranChecked(false);
        setFursanVerified(false);
        setCustomerId(customerDetailsRes?.users?.collection?.[0]?.id);
        setIsCustomerProfileCompleted(details?.isCustomerProfileCompleted);
        if (setCustomerCanBook) {
          setCustomerCanBook(details?.isCustomerProfileCompleted);
        }
        // TODO: Add message if user can't book because of age
        if (!details?.isCustomerProfileCompleted) {
          NotificationManager.info(<FormattedMessage id="usersProfileIsNotComplete" />);
        }
        setCustomerDetails(customerDetailsRes);
      }
    }
  }, [customerDetailsRes]);
  return (
    <>
      {loading && <FullPageLogoLoader />}
      <form
        onSubmit={(e) => {
          e.preventDefault();
        }}
      >
        <div className="container">
          <div className="row" style={{ justifyContent: "center" }}>
            <div className="col-sm-12 col-md-3 mt-2" dir="rtl">
              {
                isAgency ? 
                <Select
                className={`dropdown-select`}
                options={agencyCustomersQuery?.agencyCustomers?.collection.map((item)=>({
                  value:item.user.id,
                  label:item.user.name,
                  mobile:item.user.mobile
                }))}
                placeholder={<FormattedMessage id={"Select"} />}
                noOptionsMessage={() => {
               
                  if (!agencyCustomersQuery?.agencyCustomers?.collection?.length) return <FormattedMessage id={ "No data found"} />;
                }}
                onChange={(selection)=>{
                  setCustomerId(selection)
                  setSelectedCustomer(true)
                  setFullNumber(selection.mobile)
                }}
                />
                : 
                <IntlTelInput
                separateDialCode
                preferredCountries={["sa"]}
                disabled={!!customerId}
                telInputProps={{ pattern: "[0-9]*" }}
                containerClassName="intl-tel-input"
                inputClassName="form-control"
                value={customerNumber}
                onSelectFlag={(currentNumber, selectedCountryData, fullNum, isValid) => {
                  if (/^[0-9]+$/.test(currentNumber.toString()) || currentNumber === "") {
                    setCustomerNumber(currentNumber);
                    setFullNumber(fullNum.replace(/\D/gm, ""));
                    setIsNumberValid(isValid);
                  } else {
                    setCustomerNumber(customerNumber);
                  }
                }}
                onPhoneNumberChange={(isValid, num, _, fullNum) => {
                  if (/^[0-9]+$/.test(num.toString()) || num === "") {
                    setCustomerNumber(num);
                    setFullNumber(fullNum.replace(/\D/gm, ""));
                    setIsNumberValid(isValid);
                  } else {
                    setCustomerNumber(customerNumber);
                  }
                }}
              />
              }
            
            </div>
            <div className="col-sm-12 col-md-3 mt-2">
              <button
                type="submit"
                variant="contained"
                disabled={(!isNumberValid) || (isAgency && !SelectedCustomer)}
                style={{ width: "100%" }}
                className="btn btn-primary text-white btn-icon"
                onClick={() => setRequestCustomerDetails(true)}
              >
                <FormattedMessage id="customer.data" />
              </button>
            </div>
          </div>
          {customerDetailsRes?.users?.collection?.length === 0 && (
            <DangerFormattedMessage msgId="thisUserIsNotRegisteredYet" />
          )}
          {!isCustomerProfileCompleted && customerDetailsRes?.users?.collection?.[0] && (
            <DangerFormattedMessage msgId="usersProfileIsNotComplete" />
          )}
        </div>
      </form>
    </>
  );
}

GettingCustomerDetails.propTypes = {
  setCustomerDetails: PropTypes.func,
  setCustomerCanBook: PropTypes.func,
  setCustomerId: PropTypes.func,
  customerId: PropTypes.string,
};

export default GettingCustomerDetails;
