import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, But<PERSON> } from "@material-ui/core";
import { FormattedMessage } from "react-intl";
import { set } from "date-fns";

const RentalPriceFilter = ({ setMinPrice, setMaxPrice, minPrice, maxPrice, action }) => {
  const [error, setError] = useState("");
  const [maxError, setMaxError] = useState();

  const validatePrice = (value) => {
    const regex = /^\d{0,6}(\.\d{0,2})?$/;
    return regex.test(value);
  };

  const handleMinPriceChange = (e) => {
    if (e && action) action();
    const value = e.target.value;
    if (!value) {
      setMinPrice(undefined);
      setError("");
      setMaxError("");
      return;
    }
    if (validatePrice(value)) {
      if (value > 999999.99) {
        setError("Max. is 999999.99");
        setMinPrice("");
      } else if (value > maxPrice && maxPrice != undefined) {
        setError("Daily price to must greater than Daily price from ");
        setMinPrice(value);
      } else {
        setError("");
        setMinPrice(value);
      }
    }
  };

  const handleMaxPriceChange = (e) => {
    if (e && action) action();
    const value = +e.target.value;
    if (!value) {
      setMaxPrice(undefined);
      setError("");
      setMaxError("");
      return;
    }
    if (validatePrice(value)) {
      if (value > 999999.99) {
        setMaxError("Max. is 999999.99");
        setMaxPrice("");
      } else if (value < minPrice) {
        setMaxError("Daily price to must greater than Daily price from ");
        setMaxPrice(value);
      } else {
        setMaxError("");
        setError("");
        setMaxPrice(value);
      }
    }
  };

  return (
    <div className="row mt-4 mb-2">
      <div className="col-md-6">
        <TextField
          label={<FormattedMessage id="Daily price From" />}
          type="number"
          variant="outlined"
          value={minPrice}
          onChange={handleMinPriceChange}
          inputProps={{ max: maxPrice ? maxPrice : 999999.99, step: 0.01 }}
          error={!!error}
          helperText={error && <FormattedMessage id={error} />}
        />
      </div>
      <div className="col-md-6">
        <TextField
          label={<FormattedMessage id="Daily price To" />}
          type="number"
          value={maxPrice}
          onChange={handleMaxPriceChange}
          variant="outlined"
          inputProps={{ max: 999999.99, step: 0.01 }}
          error={!!maxError}
          helperText={maxError && <FormattedMessage id={maxError} />}
        />
      </div>
    </div>
  );
};

export default RentalPriceFilter;
