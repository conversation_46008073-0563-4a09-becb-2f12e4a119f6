import React ,{useState} from "react"
import { FormattedMessage, useIntl } from "react-intl";
import CustomTextField from "components/Input/CustomTextField";
import { NotificationManager } from "react-notifications";

const CouponDiscountComponent=({selectedBranch,selectedCompany,setCopounId,getCarCouponAvailable ,copounref , customerDetails,copounCode ,setCouponCode ,selectedCar,unLimited,setChanged,rentalDetails,allyExtraServicesIds,locale,bookingId })=>{
  const [couponAvailabilty, setCouponAvailability] = useState();
 
  return(
        <div>

        {selectedCompany && selectedBranch && selectedCar ? (
            <div className="row">
              <div className="col-md-4">
                <CustomTextField
                  id="CouponCode"
                  fullWidth
                  name="Discount_coupon"
                  defaultValue={
                    copounCode
                      ? copounCode
                      : copounref.current
                      ? copounref.current
                      : null
                  }
                  value={copounCode ? copounCode : ""}
                  onChange={(e) => {
                    copounref.current = e.target.value;
                    setCouponCode(e.target.value);
                    // setChanged(true)
                  }}
                  error={couponAvailabilty}
                  errormsg={couponAvailabilty}
                />
              </div>
              <div className="col-md-4">
                <div className="row " style={{ gap: "8px" }} disabled={!copounCode}>
                  <button
                    onClick={() => {
                      if (copounCode) {
                        getCarCouponAvailable({
                          variables: {
                            carId: selectedCar?.value,
                            couponCode: copounref.current,
                            userId:
                              customerDetails?.users?.collection[0]?.id ||
                              rentalDetails?.rentalDetails?.userId,
                          },
                        }).then((res) => {
                          if (!res?.data?.carCouponAvailability?.status) {
                            NotificationManager.error(
                              <FormattedMessage id="Invalid coupon" />,
                            );
                            setCouponAvailability("Invalid coupon");
                          } else {
                            setChanged(true);
                            setCopounId(res?.data?.carCouponAvailability?.coupon?.id);
                            setCouponAvailability(null);
                          }
                        });
                      } else {
                        return;
                      }
                    }}
                    className="btn btn-primary"
                  >
                    <FormattedMessage id="apply" />
                  </button>
                  <button
                    className="btn btn-danger"
                    disabled={!copounCode}
                    onClick={() => {
                      setCopounId(undefined);
                      setCouponCode(undefined);
                      copounref.current = null;
                      setCouponAvailability(null);
                      setChanged(true);
                    }}
                  >
                    <FormattedMessage id="delete" />
                  </button>
                </div>
              </div>
            </div>
          ) : null}

      
        </div>

       
    )
}
export default CouponDiscountComponent