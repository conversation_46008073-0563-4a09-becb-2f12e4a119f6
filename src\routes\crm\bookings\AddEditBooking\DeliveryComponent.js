import React from "react"
import { FormattedMessage, useIntl } from "react-intl";

const DeliveryComponent=({isDelivery,setIsDelivery,setSelectedCompany,handoverTobranch,setHandoverToBranch,setHandoverToSamePickup})=>{
  
return(
    <div className="d-flex flex-column" style={{ gap: "0.1rem", flexWrap: "wrap" }}>
                    <div className="form-check form-check-inline">
                          <input
                            className="form-check-input"
                            type="checkbox"

                            onChange={(e)=>{
                                setIsDelivery(e.target.checked)
                                setSelectedCompany({})
                            }}
                          />
                          <label className="form-check-label p-0 m-2" htmlFor={"delivery"}>
                            <FormattedMessage id="delivery" />
                          </label>
                        </div>
                        {
                            isDelivery && !handoverTobranch && 
                            <div className="d-flex" style={{ gap: "0.1rem", flexWrap: "wrap" }}>
                            {[{name:"handover_to_same_Pickup_location",type:true},{name:"return_to_the_branch",type:false}].map((item) => (
                              <div className="form-check form-check-inline" >
                                <input
                                  className="form-check-input"
                                    type="radio"
                                  onChange={(e)=>{
                                    setHandoverToSamePickup(item.type)
                                  }}
                                  name="handoverType"
                                 
                                 
                                />
                                <label className="form-check-label p-0 m-2" htmlFor={item.name}>
                                  <FormattedMessage id={item.name} />
                                </label>
                              </div>
                            ))}
                               </div>
                        }
                      
                        <div className="form-check form-check-inline">
                          <input
                            className="form-check-input"
                            type="checkbox"       
                            onChange={(e)=>setHandoverToBranch(e.target.checked)}                    
                          
                          />
                          <label className="form-check-label p-0 m-2" htmlFor={"handover"}>
                            <FormattedMessage id="handover" />
                          </label>
                        </div>
                    </div>
)
}
export default DeliveryComponent