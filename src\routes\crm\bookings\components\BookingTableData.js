/* eslint-disable prettier/prettier */
/* eslint-disable eqeqeq */
import React from "react";
import { Link } from "react-router-dom";
import { dataTypes } from "constants/constants";
import { FormattedMessage } from "react-intl";
import { statusColor } from "functions/colors";
import moment from "moment";
import store from "../../../../store";
const { PRICE, ACTIONS, FUNC, ASSIGNBOOKING, REFUNDBOOKING, TEXT } = dataTypes;

/**
 * @name DisplayDateCityTime
 * @example of text output
 *     January 12, 2022
 *     Riyadh
 *     2:00 AM
 * @param {object} record
 * @param {string} locale
 * @param {string} location "DropOffCityName" || "PickUpCityName"
 * @param {string} date     "pickUpDate" || "dropOffDate"
 * @return {JSX}
 */

function DisplayDateCityTime(record, locale, location, date, time) {
  return (
    <div style={{ minWidth: "max-content", fontSize: "small", display: "flex", gap: "7px" }}>
      {record.lastConfirmedExtensionRequest && date == "dropOffDate"
        ? moment(record.lastConfirmedExtensionRequest[date]).locale(locale).format("LL")
        : moment(record[date]).locale(locale).format("LL")}
      <div>{record[`${locale}${location}`]}</div>
      {record.lastConfirmedExtensionRequest && date == "dropOffDate"
        ? moment(record.lastConfirmedExtensionRequest[time], "HH:mm:ss")
            .locale(locale)
            .format("hh:mm A")
        : moment(record[time], "HH:mm:ss").locale(locale).format("hh:mm A")}
    </div>
  );
}
const { ally_id } = store.getState()?.authUser.user;

export const bookingTableData = [
  {
    headerId: "bookingId.placeholder",
    dataType: FUNC,
    dataRef: "id",
    orderBy: "id",
    issortable: true,
    func: (record, locale) => <Link to={`/cw/dashboard/bookings/${record.id}`}>{record.id}</Link>,
  },
  {
    headerId: "bookingNo.",
    dataType: FUNC,
    dataRef: "bookingNo",
    func: (record, locale) => (
      <Link to={`/cw/dashboard/bookings/${record.id}`}>{record.bookingNo}</Link>
    ),
  },
  {
    headerId: "bookings.list.customerName",
    dataRef: "customerName",
    dataType: FUNC,
    func: (record) => (
      <Link to={`/cw/dashboard/customers/${record.userId}`}>{record.customerName}</Link>
    ),
  },
  {
    headerId: "bookings.list.allyName",
    dataRef: "AllyName",
    dataType: FUNC,
    func: (record, locale) => (
      <Link to={`/cw/dashboard/companies/${record.allyCompanyId}`}>
        {record[`${locale}AllyName`]}
      </Link>
    ),
  },
  {
    headerId: "branch",
    dataRef: "branchName",
    dataType: FUNC,
    func: (record) => (
      <Link to={`/cw/dashboard/branches/${record.branchId}`}>{record.branchName}</Link>
    ),
  },

  {
    headerId: "car",
    bilingual: { ar: "arMakeName", en: "enMakeName" },
    dataType: FUNC,
    func: (record, locale) => {
      const text = `${record[`${locale}MakeName`]} - ${record[`${locale}ModelName`]} -${
        record[`${locale}VersionName`]
      }- ${record.year}`;
      return (
        <Link to={`/cw/dashboard/cars/${record.carId}`}>
          {text.replaceAll("--", "-").replace("-  -", "-")}
        </Link>
      );
    },
  },
  {
    headerId: "bookings.list.numberOfDaysToBeRented",
    dataRef: "numberOfDays",
    dataType: FUNC,
    func: (record) =>
      record.lastConfirmedExtensionRequest
        ? record.lastConfirmedExtensionRequest.numberOfDays
        : record.numberOfDays,
    orderBy: "number_of_days",
    issortable: true,
  },
  { headerId: "paymentMethod", dataType: REFUNDBOOKING },

  {
    headerId: "bookings.list.carRentPricePerDay",
    dataRef: "pricePerDay",
    dataType: PRICE,

    orderBy: "price_per_day",
    issortable: true,
  },
  {
    headerId: "bookings.list.header.billingAmount",
    dataRef: "totalBookingPrice",
    dataType: FUNC,
    func: (record) =>
      record.lastConfirmedExtensionRequest
        ? record.lastConfirmedExtensionRequest.totalBookingPrice
        : record.totalBookingPrice,

    orderBy: "total_booking_price",
    issortable: true,
  },
  {
    headerId: "bookings.list.paidAmount",
    dataRef: "totalInsurancePrice",
    dataType: FUNC,
    func: (record) =>
      record.lastConfirmedExtensionRequest
        ? record.lastConfirmedExtensionRequest.totalInsurancePrice
        : record.totalInsurancePrice,
  },
  {
    headerId: "businessBookings.list.pickup_date_time",
    dataRef: "pickUpDate",
    dataType: FUNC,
    func: (record, locale) =>
      DisplayDateCityTime(record, locale, "PickUpCityName", "pickUpDate", "pickUpTime"),
    orderBy: "pick_up_datetime",
    issortable: true,
  },
  {
    headerId: "businessBookings.list.dropoff_date_time",
    dataRef: "dropOffDate",
    dataType: FUNC,
    func: (record, locale) => {
      return DisplayDateCityTime(record, locale, "DropOffCityName", "dropOffDate", "dropOffTime");
    },
    orderBy: "drop_off_datetime",
    issortable: true,
  },
  {
    headerId: "bookings.list.bookingStatus",
    dataRef: "status",
    dataType: FUNC,
    func: (record) => (
      <div style={{ display: "flex", gap: "7px", alignItems: "center" }}>
        <div className="d-flex">
          {record.isIntegratedRental && <div className="badge badge-info mr-2">Api</div>}
          <div
            className={`badge badge-${statusColor(record.status)}`}
            style={{
              backgroundColor: statusColor(record.status),
              maxWidth: "fit-content",
              fontSize: "12px",
              display: "flex",
              gap: "7px",
              display: "flex",
              flexDirection: "column",
              gap: "5px",
            }}
          >
            <p className="m-0 p-0 font-weight-light" style={{ fontSize: "12px" }}>
              {record.status && <FormattedMessage id={record.status.toUpperCase()} />}
            </p>
            {/* {Boolean(record?.subStatus?.toUpperCase()) && <span>-</span>} */}
            <p className="m-0 p-0 font-weight-light" style={{ fontSize: "12px" }}>
              {record?.subStatus && <FormattedMessage id={record?.subStatus?.toUpperCase()} />}
            </p>
          </div>
        </div>
        <div style={{ fontSize: "12px", textAlign: "center" }}>
          {record.lastRentalDateExtensionRequest && <FormattedMessage id="extend" />}
        </div>
      </div>
    ),
  },

  {
    headerId: "bookings.paidamount",
    dataRef: "status",
    dataType: FUNC,
    func: (record) => record.paidAmount,
  },
  {
    headerId: "createdAt",
    dataRef: "createdAt",
    dataType: FUNC,
    func: (record, locale) => {
      if (record?.createdAt) {
        const date = moment(record.createdAt).locale(locale);
        return (
          <div style={{ display: "flex", gap: "5px" }}>
            <span style={{ fontSize: "small" }}>{date.format("LL")}</span> <br />{" "}
            <span>{date.format("hh:mm A")}</span>
          </div>
        );
      }
      return "";
    },
    orderBy: "created_at",
    issortable: true,
  },
  !ally_id ? { headerId: "Assign", dataType: ASSIGNBOOKING } : null,
  { headerId: "common.actions", dataType: ACTIONS },
];
export const bookingTableDataForAgency = [
  {
    headerId: "bookingId.placeholder",
    dataType: FUNC,
    dataRef: "id",
    orderBy: "id",
    issortable: true,
    func: (record, locale) => <Link to={`/cw/dashboard/bookings/${record.id}`}>{record.id}</Link>,
  },
  {
    headerId: "bookingNo.",
    dataType: FUNC,
    dataRef: "bookingNo",
    func: (record, locale) => (
      <Link to={`/cw/dashboard/bookings/${record.id}`}>{record.bookingNo}</Link>
    ),
  },
  {
    headerId: "bookings.list.customerName",
    dataRef: "customerName",
    dataType: FUNC,
    func: (record) => (
      <Link to={`/cw/dashboard/customers/${record.userId}`}>{record.customerName}</Link>
    ),
  },
  {
    headerId: "car",
    bilingual: { ar: "arMakeName", en: "enMakeName" },
    dataType: FUNC,
    func: (record, locale) => {
      const text = `${record[`${locale}MakeName`]} - ${record[`${locale}ModelName`]} -${
        record[`${locale}VersionName`]
      }- ${record.year}`;
      return (
        <Link to={`/cw/dashboard/cars/${record.carId}`}>
          {text.replaceAll("--", "-").replace("-  -", "-")}
        </Link>
      );
    },
  },
  {
    headerId: "businessBookings.list.pickup_date_time",
    dataRef: "pickUpDate",
    dataType: FUNC,
    func: (record, locale) =>
      DisplayDateCityTime(record, locale, "PickUpCityName", "pickUpDate", "pickUpTime"),
    orderBy: "pick_up_datetime",
    issortable: true,
  },
  {
    headerId: "businessBookings.list.dropoff_date_time",
    dataRef: "dropOffDate",
    dataType: FUNC,
    func: (record, locale) => {
      return DisplayDateCityTime(record, locale, "DropOffCityName", "dropOffDate", "dropOffTime");
    },
    orderBy: "drop_off_datetime",
    issortable: true,
  },
  {
    headerId: "bookings.list.numberOfDaysToBeRented",
    dataRef: "numberOfDays",
    dataType: FUNC,
    func: (record) =>
      record.lastConfirmedExtensionRequest
        ? record.lastConfirmedExtensionRequest.numberOfDays
        : record.numberOfDays,
    orderBy: "number_of_days",
    issortable: true,
  },
  { headerId: "paymentMethod", dataType: REFUNDBOOKING },
  {
    headerId: "bookings.list.bookingStatus",
    dataRef: "status",
    dataType: FUNC,
    func: (record) => (
      <div style={{ display: "flex", gap: "7px", alignItems: "center" }}>
        <div className="d-flex">
          {record.isIntegratedRental && <div className="badge badge-info mr-2">Api</div>}
          <div
            className={`badge badge-${statusColor(record.status)}`}
            style={{
              backgroundColor: statusColor(record.status),
              maxWidth: "fit-content",
              fontSize: "12px",
              display: "flex",
              gap: "7px",
              display: "flex",
              flexDirection: "column",
              gap: "5px",
            }}
          >
            <p className="m-0 p-0 font-weight-light" style={{ fontSize: "12px" }}>
              {record.status && <FormattedMessage id={record.status.toUpperCase()} />}
            </p>
            {/* {Boolean(record?.subStatus?.toUpperCase()) && <span>-</span>} */}
            <p className="m-0 p-0 font-weight-light" style={{ fontSize: "12px" }}>
              {record?.subStatus && <FormattedMessage id={record?.subStatus?.toUpperCase()} />}
            </p>
          </div>
        </div>
        <div style={{ fontSize: "12px", textAlign: "center" }}>
          {record.lastRentalDateExtensionRequest && <FormattedMessage id="extend" />}
        </div>
      </div>
    ),
  },
  { headerId: "common.actions", dataType: ACTIONS },
];
