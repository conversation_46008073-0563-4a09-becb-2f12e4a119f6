import React from "react"
import Select from "react-select";
const InsuranceDropDown=({selectedCar,setChanged,setInsuranceId,insuranceId})=>{
    console.log(selectedCar,"selectedCar")
return(
    <Select
    id="insurance"
    className="mt-4"
    options={selectedCar?.carInsurances?.map((i) => ({
      label: i.insuranceName,
      value: i.id,
    }))}
    disabled={
      !selectedCar || !Array.isArray(selectedCar?.carInsurances)
    }
    value={selectedCar?.carInsurances
      ?.map((i) => ({ label: i.insuranceName, value: i.id }))
      ?.find((i) => i.value == insuranceId)}
    onChange={(selection) => {
      setInsuranceId(selection.value);
      setChanged(true);
    }}
  />
)
}
export default InsuranceDropDown