/* eslint-disable prettier/prettier */
/**
 * Sidebar Content
 */
import React, { Component } from "react";
import PropTypes from "prop-types";
import List from "@material-ui/core/List";
import { withRouter } from "react-router-dom";
import { connect, useSelector } from "react-redux";
import { onToggleMenu } from "actions";
import { userCan } from "functions/userCan";
import NavMenuItem from "./NavMenuItem";
import "./style.css";

const sideBarContent = (userType) => [
  {
    permissions: ["rentals.list"],
    menu_icon: "ti-stats-up",
    path: "/cw/dashboard/Statistics",
    new_item: false,
    menu_title: "sidebar.statistics",
  },
  {
    permissions: [
      "rentals.list",
      "rentals.cancel",
      "rentals.update_status",
      "rentals.update",
      "rentals.create",
      userType?.includes("agency") ? "no.permission" : "",
    ],
    menu_icon: "ti-briefcase",
    path: "/cw/dashboard/bookings",
    new_item: false,
    menu_title: "sidebar.bookings",
  },
  {
    parent: true,
    title: "Carwah Business",
    menu_icon: "ti-folder",
    children: [
      {
        path: "/cw/dashboard/businessBookings",
        new_item: false,
        menu_title: "businessBookings",
        permissions: [
          "business_rentals.list",
          "business_rentals.close",
          "business_rentals.view",
          "business_rentals.update",
        ],
      },
      {
        path: "/cw/dashboard/businessrequests",
        new_item: false,
        menu_title: "sidebar.businessrequests",
        permissions: [
          "business_requests.list",
          "business_requests.view",
          "business_requests.offer",
          "business_requests.create",
          "business_requests.update",
        ],
      },
    ],
  },
  {
    permissions: [
      "users.list",
      "users.create",
      "users.show",
      "users.update",
      "users.activation",
      "users.delete",
    ],
    menu_icon: "ti-user",
    path: "/cw/dashboard/customers",
    new_item: false,
    menu_title: "sidebar.customers",
  },
  {
    parent: true,
    title: "Allies",
    menu_icon: "ti-shield",
    children: [
      {
        permissions: [
          "ally_companies.create",
          "ally_companies.update",
          "ally_companies.activation",
        ],
        path: "/cw/dashboard/companies",
        new_item: false,
        menu_title: "sidebar.companies",
      },
      {
        permissions: [
          "branches.create",
          "branches.update",
          "branches.activation",
          !userType?.includes("agency") ? "no.permission" : "",
        ],
        path: "/cw/dashboard/branches",
        new_item: false,
        menu_title: "sidebar.branches",
      },
      {
        permissions: [
          "ally_companies.create",
          "ally_companies.update",
          "ally_companies.activation",
        ],
        path: "/cw/dashboard/managers",
        new_item: false,
        menu_title: "sidebar.managers",
      },
      {
        permissions: [
          "users.list",
          "users.create",
          "users.show",
          "users.update",
          "users.activation",
          "users.delete",
        ],
        path: "/cw/dashboard/allies-rates",
        new_item: false,
        menu_title: "sidebar.alliesRates",
      },
    ],
  },
  {
    parent: true,
    title: "Cars",
    menu_icon: "ti-car",
    children: [
      {
        permissions: ["cars.create", "cars.update", "cars.activation"],

        path: "/cw/dashboard/cars",
        new_item: false,
        menu_title: "sidebar.cars",
      },
      {
        permissions: ["makes.delete", "makes.update", "makes.create"],

        path: "/cw/dashboard/makes",
        new_item: false,
        menu_title: "sidebar.makes",
      },
      {
        permissions: ["car_models.create", "car_models.update", "car_models.delete"],

        path: "/cw/dashboard/models",
        new_item: false,
        menu_title: "sidebar.models",
      },
      {
        permissions: ["car_versions.create", "car_versions.update", "car_versions.delete"],

        path: "/cw/dashboard/versions",
        new_item: false,
        menu_title: "sidebar.versions",
      },
      {
        permissions: [
          "users.list",
          "users.create",
          "users.show",
          "users.update",
          "users.activation",
          "users.delete",
        ],
        path: "/cw/dashboard/features",
        new_item: false,
        menu_title: "sidebar.features",
      },
    ],
  },
  {
    parent: true,
    menu_icon: "ti-id-badge",
    title: "Roles & Permissions",
    children: [
      {
        permissions: [
          "roles.list",
          "roles.create",
          "roles.show",
          "roles.update",
          "roles.activation",
          "roles.delete",
        ],
        path: "/cw/dashboard/roles",
        new_item: false,
        menu_title: "sidebar.roles",
      },
      {
        permissions: [
          "users.list",
          "users.create",
          "users.show",
          "users.update",
          "users.activation",
          "users.delete",
        ],
        path: "/cw/dashboard/users",
        new_item: false,
        menu_title: "sidebar.users",
      },
    ],
  },
  {
    parent: true,
    menu_icon: "zmdi zmdi-settings",
    title: "Settings",
    children: [
      {
        permissions: ["banners.delete", "banners.view", "banners.update", "banners.create"],
        path: "/cw/dashboard/banners",
        new_item: false,
        menu_title: "sidebar.banners",
      },

      {
        permissions: [
          "users.list",
          "users.create",
          "users.show",
          "users.update",
          "users.activation",
          "users.delete",
        ],
        path: "/cw/dashboard/extraservice",
        new_item: false,
        menu_title: "sidebar.service",
      },

      {
        permissions: ["coupons.list"],
        path: "/cw/dashboard/coupons",
        new_item: false,
        menu_title: "coupons",
      },
      {
        permissions: ["highly_requested_packages.list"],
        path: "/cw/dashboard/packages",
        new_item: false,
        menu_title: "sidebar.packages",
      },
      {
        permissions: ["loyalty_settings.list"],
        path: "/cw/dashboard/LoyaltyPoints",
        new_item: false,
        menu_title: "sidebar.LoyaltyPoints",
      },
      {
        path: "/cw/dashboard/Support",
        new_item: false,
        menu_title: "sidebar.support",
        permissions: ["feedbacks.list"],
      },
    ],
  },
  {
    // parent: true,
    menu_icon: "zmdi zmdi-city",
    menu_title: "Agencies",
    permissions: userType === "carwah_admin" ? ["no.permission"] : ["agencies.list"],
    path: "/cw/dashboard/agencies",
    // children: [
    //   {
    //     new_item: false,
    //     menu_title: "sidebar.AgenciesList",
    //   },
    // ],
  },
  {
    // parent: true,
    menu_icon: "ti-user",
    menu_title: "sidebar.customers",
    permissions:
      userType === "agency_admin" || userType === "agency_customerService"
        ? ["agencies.manage_users"]
        : [],
    path: "/cw/dashboard/customers",
  },
  {
    parent: true,
    menu_icon: "zmdi zmdi-settings",
    title: "Settings",
    children: [
      {
        permissions: userType === "agency_admin" ? ["agencies.view"] : [],
        path: "/cw/dashboard/profile",
        new_item: false,
        menu_title: "Profile",
      },
      {
        permissions: userType === "agency_admin" ? ["no.permission"] : [],
        path: "/cw/dashboard/wallet",
        new_item: false,
        menu_title: "sidebar.wallet",
      },
      {
        permissions: userType === "agency_admin" ? ["agencies.manage_users"] : [],
        path: "/cw/dashboard/users",
        new_item: false,
        menu_title: "sidebar.users",
      },
    ],
  },
  ,
];
class SidebarContent extends Component {
  toggleMenu(menu, stateCategory) {
    const data = {
      menu,
      stateCategory,
    };
    this.props.onToggleMenu(data);
  }

  render() {
    const { state } = this.props; // Access the state from props
    const { user } = state.authUser || {};
    return (
      <div className="rct-sidebar-nav my-2">
        <nav className="navigation">
          <List className="rct-mainMenu p-0 m-0 list-unstyled">
            {sideBarContent(
              user?.user_type?.includes("agency")
                ? ["agency_admin", "agency_super_admin"].includes(user?.user_type)
                  ? "agency_admin"
                  : "agency_customerService"
                : user?.ally_id ?  "" :  "carwah_admin",
            ).map((menu, key) => (
              <NavMenuItem
                menu={menu}
                key={JSON.stringify(key)}
                onToggleMenu={() => this.toggleMenu(menu, "category1")}
                userCan={userCan}
              />
            ))}
          </List>
        </nav>
      </div>
    );
  }
}

SidebarContent.propTypes = {
  onToggleMenu: PropTypes.func,
};

// map state to props
const mapStateToProps = (state) => ({ state });

export default withRouter(
  connect(mapStateToProps, {
    onToggleMenu,
  })(SidebarContent),
);
