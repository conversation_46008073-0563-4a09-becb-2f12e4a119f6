/* eslint-disable prettier/prettier */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable eqeqeq */
/* eslint-disable no-undefined */
/** Bookings/Rentals Page */
import React, { useState, useEffect } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import Paper from "@material-ui/core/Paper";
import Tabs from "@material-ui/core/Tabs";
import Tab from "@material-ui/core/Tab";
import { useLocation, useHistory } from "react-router-dom";
import { Helmet } from "react-helmet";
import Button from "@material-ui/core/Button";
import { GetBookingsQuery } from "gql/queries/Rental.queries.gql";
import { RentalsCount } from "gql/queries/RentalsCount.gql";
import { useQuery } from "@apollo/client";
import PageTitleBar from "components/PageTitleBar/PageTitleBar";
import { RctCard, RctCardContent } from "Components/RctCard";
import IntlMessages from "util/IntlMessages";
import { FiltersAndSearches } from "components/FiltersAndSearches";
import { NotificationManager } from "react-notifications";
import { userCan, getPageFromHash } from "functions";
import AppBar from "@material-ui/core/AppBar";
import Badge from "@material-ui/core/Badge";
import Grow from "@material-ui/core/Grow";
import { Users } from "gql/queries/CustomerCare.gql";
import { Collapse } from "@material-ui/core";
import FilterListIcon from "@material-ui/icons/FilterList";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import ExpandLessIcon from "@material-ui/icons/ExpandLess";
import BookingsList from "./components/BookingsList";
import store from "../../../store";
import useRentalStatus from "./useRentalStatus";
import { useSelector } from "react-redux";
import { AgencyDtails } from "gql/queries/AgencyDetails.gql";

export default function Bookings() {
  const location = useLocation();
  const history = useHistory();
  const { user } = useSelector((state) => state.authUser) || {};
  const { user_type } = user || {};
  const { data: agency } = useQuery(AgencyDtails, {
    skip: !user?.agency_id,
    variables: { id: +user?.agency_id },
  });
  const [count, setCount] = useState();
  const { formatMessage, messages } = useIntl();
  const [query, setQuery] = useState({});
  const [page, setPage] = useState(getPageFromHash(history) || 1);
  const [limit, setLimit] = useState(50);
  const { ally_id } = store.getState()?.authUser.user;
  const [orderBy, setOrderBy] = useState("pick_up_datetime");
  const [sortBy, setSortBy] = useState();
  const decodedSearch =
    history?.location?.search && JSON.parse(decodeURI(history?.location?.search?.slice(1)));
  const {
    data: bookingsRes,
    refetch,
    loading,
    error,
  } = useQuery(GetBookingsQuery, {
    skip: !userCan("rentals.list"),
    variables: {
      page,
      limit,
      orderBy:
        query?.subStatus?.includes("ally_declined") && ally_id
          ? "ally_declined_sort"
          : query?.subStatus?.includes("due_invoice") && !orderBy
          ? "drop_off_date"
          : orderBy,
      sortBy:
        query?.subStatus?.includes("ally_declined") || query?.status?.includes("pending")
          ? "desc"
          : query?.subStatus?.includes("due_invoice") && !sortBy
          ? "desc"
          : sortBy,
      ...query,
      makeName: query.makeName == "all" ? undefined : query.makeName,
      paymentMethod: query.paymentMethod == "ALL" ? undefined : query.paymentMethod,
      plateNo: query?.plateNo ? query?.plateNo : undefined,

      cityName:
        query?.cityName?.length && !query?.cityName?.includes("all") ? query?.cityName : undefined,
      status: query.status == "all" ? undefined : query.status,
      paymentStatusFilter: query?.paymentStatusFilter?.includes("all")
        ? undefined
        : query?.paymentStatusFilter,
      subStatus: query.subStatus == "all" ? undefined : query.subStatus,
      userId: decodedSearch?.userId,
      userNid: query.userNid?.length ? query.userNid : null,
      allyCompanyId: query.allyCompanyId == "all" ? null : query.allyCompanyId,
      airportId: query.airportId == "all" ? undefined : query.airportId,
      agencyId: query?.agency?.value || undefined,
    },
  });
  const { data: users } = useQuery(Users);
  const { data: rentalcount } = useQuery(RentalsCount, {
    variables: {
      userId: decodedSearch?.userId,
    },
  });
  const [value, setValue] = React.useState(0);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { combinedStatuses, rentalSubStatuses } = useRentalStatus();
  const user_data = JSON.parse(localStorage.getItem("user_data"));
  const isAgency = user_data?.agency_id;
  const handleChange = (e, value, index) => {
    const valuesCombinedStatuses = Object.entries(combinedStatuses);
    const statusItem = valuesCombinedStatuses?.find((array) => array[1] == value[0])?.[0];
    setValue(index);
    const isSubStatus = Boolean(rentalSubStatuses[statusItem]);

    if (statusItem == "all") {
      setPage(1);

      setQuery({ status: null });
      history.replace({ search: "" });
      return;
    }
    if (isSubStatus) {
      // setValue(index);
      setPage(1);
      setQuery({ subStatus: statusItem });
      history.replace({
        search: JSON.stringify({ subStatus: statusItem, localizedSubStatus: value[0] }),
      });
    } else {
      setPage(1);
      // setValue(index);
      setQuery({ status: statusItem == "all" ? null : statusItem });
      history.replace({
        search: JSON.stringify({ status: statusItem, localizedStatus: value[0] }),
      });
    }
  };
  useEffect(() => {
    refetch();
  }, []);

  useEffect(() => {
    if (error?.message) NotificationManager.error(error.message);
  }, [error]);
  useEffect(() => {
    if (rentalcount) {
      setCount(rentalcount?.rentalsCount.all);
    }
  }, [rentalcount, count]);
  return (
    <div className="clients-wrapper">
      <Helmet>
        <title>{formatMessage({ id: "sidebar.bookings" })}</title>
      </Helmet>
      <PageTitleBar
        title={<IntlMessages id="sidebar.bookings" />}
        match={location}
        enableBreadCrumb
        extraButtons={
          <>
            {((userCan("rentals.create") && !ally_id && !agency) ||
              agency?.agency?.isActive) && (
              <Button
                variant="contained"
                color="primary"
                className="btn btn-success"
                onClick={() => history.push( "bookings/add")}
              >
                <IntlMessages id="create.new.something" values={{ something: messages?.booking }} />
              </Button>
            )}
          </>
        }
      />
      {userCan("rentals.list") || user_type?.includes("agency") ? (
        <div className="d-flex">
          <div
            className="search-bar-wrap"
            style={{
              width: "calc(100% - 70px)",
              visibility: !isCollapsed && user_type?.includes("agency") ? "hidden" : "visible",
            }}
          >
            <RctCard>
              <RctCardContent>
                <Collapse in={isCollapsed} timeout="auto">
                  <div className="mb-4">
                    <FiltersAndSearches
                      refetch={refetch}
                      setValue={setValue}
                      setPage={setPage}
                      inBooking
                      forBooking
                      query={query}
                      setQuery={setQuery}
                      submitbtnid="search.filter"
                      count={count}
                      fields={[
                        { type: "search", name: "customerName" },
                        { type: "search", name: "userNid" },
                        { type: "search", name: "bookingNo" },
                        !agency ? { type: "search", name: "plateNo" } : undefined,
                      ]}
                      filters={[
                        "makes",
                        "cities",
                        "status",
                        !agency ? "substatus" : undefined,
                        !agency ? "payment" : undefined,
                        !agency ? "ally" : undefined,
                        !agency ? "paymentStatusFilter" : undefined,
                        !agency ? "airports" : undefined,
                        !agency ? "RentType" : undefined,
                        !agency ? "paymentBrand" : undefined,
                        !agency ? "agenciesFilter" : undefined,
                      ]}
                      pickupDateFilter
                      dropoffDateFilter
                      mobile
                      mobileRef="customerMobile"
                    />
                  </div>
                </Collapse>
                {!user_type?.includes("agency") ? (
                  <div
                    style={{
                      flexGrow: 1,
                      width: "100%",
                    }}
                  >
                    <Paper square>
                      <AppBar position="static" color="default">
                        <Grow in>
                          <Tabs
                            value={value}
                            // onChange={handleChange}
                            indicatorColor="primary"
                            textColor="primary"
                            variant="scrollable"
                            scrollButtons="auto"
                            aria-label="scrollable auto tabs example"
                          >
                            {count &&
                              count.map((filterbook, index) => (
                                <Tab
                                  onClick={(e) => handleChange(e, filterbook, index)}
                                  label={filterbook?.[0]}
                                  icon={
                                    <Badge
                                      color="secondary"
                                      anchorOrigin={{
                                        vertical: "top",
                                        horizontal: "right",
                                      }}
                                    >
                                      {filterbook?.[1]}
                                    </Badge>
                                  }
                                />
                              ))}
                          </Tabs>
                        </Grow>
                      </AppBar>
                    </Paper>
                  </div>
                ) : null}
              </RctCardContent>
            </RctCard>
          </div>
          <div className="d-flex justify-content-end">
            <Button
              className="d-flex justify-content-end mb-2 align-items-center"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              <FilterListIcon />
              <span>
                <FormattedMessage id="Filter" />
              </span>
              {!isCollapsed ? <ExpandMoreIcon /> : <ExpandLessIcon />}
            </Button>
          </div>
        </div>
      ) : null}
      {userCan("rentals.list") || user_type?.includes("agency") ? (
        <BookingsList
          refetch={refetch}
          loading={loading}
          setPage={setPage}
          page={page}
          setLimit={setLimit}
          limit={limit}
          bookingsRes={bookingsRes}
          users={users?.users?.collection}
          setOrderBy={setOrderBy}
          setSortBy={setSortBy}
          decodedUserId={decodedSearch?.userId}
          agency={agency?.agency}
        />
      ) : null}
    </div>
  );
}
