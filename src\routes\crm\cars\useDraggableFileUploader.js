/* eslint-disable prettier/prettier */
/* eslint-disable jsx-a11y/media-has-caption */
import React, { useState } from "react";

function useDraggableFileUploader() {
  const [ownCarMedia, setownCarMedia] = useState([]);
  const [draggedItem, setDraggedItem] = useState(null);

  const onDragStart = (event, index) => {
    setDraggedItem(ownCarMedia[index]);
    event.dataTransfer.effectAllowed = "move";
    event.dataTransfer.setData("text/html", event.target.parentNode);
    event.dataTransfer.setDragImage(event.target.parentNode, 20, 20);
  };

  const onDragOver = (index) => {
    const draggedOverItem = ownCarMedia[index];

    // if the item is dragged over itself, ignore
    if (draggedItem === draggedOverItem) {
      return;
    }

    // filter out the currently dragged item
    const items = ownCarMedia.filter((item) => item !== draggedItem);

    // add the dragged item after the dragged over item
    items.splice(index, 0, draggedItem);
    items.map((item, index) => ({ ...item, displayOrder: index }));
    setownCarMedia(items);
  };

  const onDrop = (event) => {
    event.preventDefault();
    setDraggedItem(null);
  };
  // Function to render media
  const renderMedia = (item, index) => (
    <div
      key={index}
      draggable
      onDragStart={(e) => onDragStart(e, index)}
      onDragOver={() => onDragOver(index)}
      onDrop={onDrop}
      style={{ margin: "10px", border: "1px dashed gray", padding: "10px", cursor: "move" }}
    >
      {item.mediaType === "video" ? (
        <video width="320" height="240" controls>
          <source src={item.mediaUrl} type="video/mp4" />
        </video>
      ) : (
        <img
          src={item.mediaUrl}
          style={{ width: "100%" }}
          alt="Uploaded Content"
          width="320"
          height="240"
        />
      )}
    </div>
  );
  return {
    ownCarMedia,
    setownCarMedia,
    draggedItem,
    setDraggedItem,
    renderMedia,
    onDrop,
    onDragOver,
    onDragStart,
  };
}

export default useDraggableFileUploader;
