/* eslint-disable prettier/prettier */
import React from "react";
import { dataTypes } from "constants/constants";
import { FormattedMessage } from "react-intl";
import { Link } from "react-router-dom";
import moment from "moment";
import YakeenVerification from "routes/components/Yakeen";
import { Tooltip } from "@material-ui/core";

const { TEXT, ACTIONS, FUNC } = dataTypes;

export const customersTableData = [
  {
    headerId: "customers.list.customerId",
    dataType: FUNC,
    dataRef: "id",
    func: (record) => (
      <Link to={`/cw/dashboard/customers/${record?.id || record?.user?.id}`}>
        {record?.id || record?.user?.id}
      </Link>
    ),
  },
  {
    headerId: "customers.list.customerName",
    dataType: FUNC,
    dataRef: "name",
    func: (record, locale, refetch) => (
      <div className="d-flex" style={{ gap: "7px", alignItems: "center" }}>
        <Link to={`/cw/dashboard/customers/${record?.id || record?.user?.id}`}>
          <div
            style={{
              display: "flex",
              flexDirection: "row-reverse",
              alignItems: "center",
              gap: "5px",
            }}
          >
            {
              record.agencyCustomerProfiles?.length ? 
              <i className="zmdi zmdi-city" />
              : null
            }
            <YakeenVerification
              {...{
                yakeenTriesLeft: record?.customerProfile?.yakeenTriesLeft,
                isYakeenVerified: record?.customerProfile?.isYakeenVerified,
                userId: record?.id,
                refetch,
                hideAction: true,
              }}
            />
            {record?.customerProfile?.blockingStatus ? (
              <Tooltip
                title={<FormattedMessage id={record?.customerProfile?.blockingStatus} />}
                placement="top"
              >
                <span>{record?.name || record?.user?.name}</span>
              </Tooltip>
            ) : (
              <span>{record?.name || record?.user?.name}</span>
            )}
          </div>
        </Link>
      </div>
    ),
  },
  {
    headerId: "customers.list.customerPhoneNumber",
    dataType: FUNC,
    dataRef: "mobile",
    func: (record) => (
      <Link to={`/cw/dashboard/customers/${record?.id || record?.user?.id}`}>
        {record?.mobile || record?.user?.name}
      </Link>
    ),
  },
  {
    headerId: "customers.list.customerEmail",
    dataType: FUNC,
    dataRef: "email",
    func: (record) => (
      <Link to={`/cw/dashboard/customers/${record?.id || record?.user?.id}`}>
        {record?.email || record?.user?.email}
      </Link>
    ),
  },

  // TODO: Add Completed trips
  // { headerId: "customers.list.numberOfCompletedTrips", dataType: TEXT, dataRef: "completedTrips" },
  {
    headerId: "bookings",
    dataType: FUNC,
    dataRef: "booking",
    func: (record) => (
      <>
        <div>
          <Link to={`bookings?{"userId":"${record?.id || record?.user?.id}"}`}>
            <FormattedMessage id="bookings" />
          </Link>
        </div>
      </>
    ),
  },
  {
    headerId: "customers.list.createdDate",
    dataType: FUNC,
    dataRef: "createdAt",
    func: (record, locale) => {
      if (record?.createdAt) {
        const date = moment(record?.createdAt).locale(locale);
        return (
          <div style={{ display: "flex", gap: "7px" }}>
            <span>{date.format("LL")}</span> <span>{date.format("hh:mm A")}</span>
          </div>
        );
      }
      return "";
    },
  },
  {
    headerId: "customers.list.customerStatus",
    dataType: FUNC,
    dataRef: "status",
    func: (record) => (
      <span className={`badge badge-${record?.isActive ? "success" : "danger"}`}>
        <FormattedMessage id={record?.isActive ? "active" : "inactive"} />
      </span>
    ),
  },
  { headerId: "common.actions", dataType: ACTIONS },
];
export const agencyCustomersTableData = [
  {
    headerId: "customers.list.customerId",
    dataType: FUNC,
    dataRef: "id",
    func: (record) => (
      <Link to={`/cw/dashboard/customers/${record?.user?.id}`}>{record?.user?.id}</Link>
    ),
  },
  {
    headerId: "customers.list.customerName",
    dataType: FUNC,
    dataRef: "name",
    func: (record, locale, refetch) => (
      <div className="d-flex" style={{ gap: "7px", alignItems: "center" }}>
        <Link to={`/cw/dashboard/customers/${record?.user?.id}`}>
          <div
            style={{
              display: "flex",
              flexDirection: "row-reverse",
              alignItems: "center",
              gap: "5px",
            }}
          >
            <YakeenVerification
              {...{
                yakeenTriesLeft: record?.customerProfile?.yakeenTriesLeft,
                isYakeenVerified: record?.customerProfile?.isYakeenVerified,
                userId: record?.id,
                refetch,
                hideAction: true,
              }}
            />
            {record?.customerProfile?.blockingStatus ? (
              <Tooltip
                title={<FormattedMessage id={record?.customerProfile?.blockingStatus} />}
                placement="top"
              >
                <span>{record?.user?.name}</span>
              </Tooltip>
            ) : (
              <span>{record?.user?.name}</span>
            )}
          </div>
        </Link>
      </div>
    ),
  },
  {
    headerId: "customers.list.customerPhoneNumber",
    dataType: FUNC,
    dataRef: "mobile",
    func: (record) => (
      <Link to={`/cw/dashboard/customers/${record?.user?.id}`}>{record?.user?.mobile}</Link>
    ),
  },
  {
    headerId: "customers.list.customerEmail",
    dataType: FUNC,
    dataRef: "email",
    func: (record) => (
      <Link to={`/cw/dashboard/customers/${record?.user?.id}`}>{record?.user?.email}</Link>
    ),
  },

  // TODO: Add Completed trips
  // { headerId: "customers.list.numberOfCompletedTrips", dataType: TEXT, dataRef: "completedTrips" },
  {
    headerId: "bookings",
    dataType: FUNC,
    dataRef: "booking",
    func: (record) => (
      <>
        <div>
          <Link to={`bookings?{"userId":"${record?.user?.id}"}`}>
            <FormattedMessage id="bookings" />
          </Link>
        </div>
      </>
    ),
  },
  {
    headerId: "customers.list.createdDate",
    dataType: FUNC,
    dataRef: "createdAt",
    func: (record, locale) => {
      if (record?.createdAt) {
        const date = moment(record?.user?.createdAt).locale(locale);
        return (
          <div style={{ display: "flex", gap: "7px" }}>
            <span>{date.format("LL")}</span> <span>{date.format("hh:mm A")}</span>
          </div>
        );
      }
      return "";
    },
  },
  {
    headerId: "customers.list.customerStatus",
    dataType: FUNC,
    dataRef: "status",
    func: (record) => (
      <span className={`badge badge-${record?.isActive ? "success" : "danger"}`}>
        <FormattedMessage id={record?.isActive ? "active" : "inactive"} />
      </span>
    ),
  },
  { headerId: "common.actions", dataType: ACTIONS },
];
