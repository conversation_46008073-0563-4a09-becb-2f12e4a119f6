import React from "react";
import { useQuery } from "@apollo/client";
import { AgencyDtails } from "gql/queries/AgencyDetails.gql";
import { Profile } from "gql/queries/AdminProfile.gql";
import { useSelector } from "react-redux";
import { useIntl } from "react-intl";

export default function UserProfile() {
  const { user } = useSelector((state) => state.authUser) || {};
  const { data: userInfo } = useQuery(Profile);
  const { data: agency, refetch } = useQuery(AgencyDtails, {
    skip: !user?.agency_id,
    variables: { id: +user?.agency_id },
  });
  const { agencyKey } = agency?.agency || {};
  const { formatMessage } = useIntl();

  return (
    <>
      <p className="m-0" style={{ textAlign: "center" }}>
        {!user?.agency_id ? userInfo?.profile?.name : user?.full_name}
      </p>
      <p
        title={formatMessage({ id: "Agency Key" })}
        style={{ fontWeight: "bold", textAlign: "center" }}
      >
        {agencyKey}
      </p>
    </>
  );
}
